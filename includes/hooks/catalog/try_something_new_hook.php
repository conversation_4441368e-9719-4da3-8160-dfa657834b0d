<?php
register_hook('display_try_something_new', function ($page = null) {
    global $cart;

    $service = new TrySomethingNew();

    if (!$service->customerId() || !$service->hasEligibleProducts()) {
        return;
    }

    $offerInCart = is_object($cart) && $cart->in_cart(TrySomethingNew::offerProductId());

    $redirectPage = $page === 'checkout_payment' ? FILENAME_CHECKOUT_PAYMENT : FILENAME_SHOPPING_CART;
    $url = tep_href_link($redirectPage, 'action=buy_now&products_id=' . (int)TrySomethingNew::offerProductId());

    // Determine if this is the checkout_payment page
    $isCheckoutPayment = ($page === 'checkout_payment');

    // Set CSS classes based on page
    $containerClasses = $isCheckoutPayment
        ? 'pure-u-24-24 pure-u-sm-24-24 pure-u-md-24-24 pure-u-lg-16-24 pure-u-xl-17-24'
        : '';
?>
    <style>
        .try-new-upsell {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 18px;
            background: linear-gradient(135deg, #fff9e6 0%, #fff4cf 100%);
            border-radius: 16px;
            box-shadow: 0 14px 30px -22px rgba(17, 23, 37, 0.6);
            color: #2d2a24;
            margin-bottom: 24px;
            padding: 18px 24px;
            letter-spacing: 0.01em;
        }

        .try-new-upsell__status {
            margin: 6px 0 0;
            padding: 8px 14px;
            font-size: 13px;
            font-weight: 600;
            color: #1f5d4a;
            background: rgba(31, 93, 74, 0.12);
            border-radius: 999px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .try-new-upsell__status:before {
            content: '\2713';
            display: inline-block;
            font-size: 14px;
        }

        .try-new-upsell__body {
            display: flex;
            align-items: center;
            gap: 18px;
            flex-wrap: nowrap;
        }

        .try-new-upsell__icon {
            flex: 0 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, #fff5d2, #fce29d);
            font-size: 26px;
            font-weight: 700;
            color: #b5872f;
            box-shadow: 0 10px 16px -14px rgba(181, 135, 47, 0.9);
        }

        .try-new-upsell__copy {
            display: flex;
            flex-direction: column;
            gap: 6px;
            line-height: 1.35;
            text-align: left;
        }

        .try-new-upsell__heading {
            margin: 0;
            font-size: 18px;
            font-weight: 700;
        }

        .try-new-upsell__subheading {
            margin: 0;
            font-size: 13px;
            color: #655d4b;
        }

        .try-new-upsell__cta {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border: none;
            border-radius: 999px;
            padding: 12px 30px;
            background: linear-gradient(135deg, #26355e 0%, #1a2544 100%);
            color: #fff;
            font-weight: 600;
            letter-spacing: 0.01em;
            transition: background 0.2s ease, box-shadow 0.2s ease, transform 0.2s ease;
            cursor: pointer;
            width: auto;
            min-width: 180px;
            max-width: 260px;
            align-self: center;
        }

        /* Checkout payment page specific styles */
        .checkout-payment-try-new .try-new-upsell__cta {
            min-width: 200px;
            max-width: 280px;
            padding: 14px 32px;
            font-size: 15px;
            font-weight: 700;
            text-decoration: none;
        }

        .checkout-payment-try-new .try-new-upsell {
            margin-bottom: 20px;
            padding: 20px 24px;
        }

        @media (max-width: 640px) {
            .try-new-upsell__body {
                flex-wrap: wrap;
                justify-content: center;
                text-align: center;
            }

            .try-new-upsell__copy {
                text-align: center;
            }

            .try-new-upsell__cta {
                width: 100%;
                max-width: none;
            }
        }

        .try-new-upsell__cta:hover,
        .try-new-upsell__cta:focus {
            background: linear-gradient(135deg, #314678 0%, #202f55 100%);
            box-shadow: 0 12px 18px -16px rgba(32, 47, 85, 0.9);
            transform: translateY(-1px);
            color: #fff;
        }

        .try-new-upsell__cta:focus-visible {
            outline: 2px solid rgba(49, 70, 120, 0.45);
            outline-offset: 2px;
        }
    </style>

    <?php if ($isCheckoutPayment) : ?>
    <div class="<?= $containerClasses; ?>">
        <div class="content-wrapper-checkout">
    <?php endif; ?>

    <div class="try-new-upsell<?= $isCheckoutPayment ? ' checkout-payment-try-new' : ''; ?>" role="complementary" aria-label="Try Something New Cigar offer">
        <div class="try-new-upsell__body">
            <div class="try-new-upsell__icon" aria-hidden="true">?</div>
            <div class="try-new-upsell__copy">
                <?php if ($offerInCart) : ?>
                    <p class="try-new-upsell__heading">Mystery cigar secured!</p>
                    <p class="try-new-upsell__status" role="status">We'll hand-pick something special for this order.</p>
                <?php else : ?>
                    <p class="try-new-upsell__heading">Try Something New Cigar</p>
                    <p class="try-new-upsell__subheading">A mystery cigar chosen just for you with a value of &pound;15 or more</p>
                <?php endif; ?>
            </div>
        </div>
        <?php if (!$offerInCart) : ?>
            <a class="try-new-upsell__cta pure-button" href="<?= $url; ?>">Add to Cart</a>
        <?php endif; ?>
    </div>

    <?php if ($isCheckoutPayment) : ?>
        </div>
    </div>
    <?php endif; ?>
<?php });
?>